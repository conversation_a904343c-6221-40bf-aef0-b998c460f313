#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试输入速度 - 测试不同的输入方法和速度
"""

from DrissionPage import ChromiumPage, ChromiumOptions
import time

def test_input_speed():
    """测试输入速度"""
    print("🚀 测试输入速度")
    print("测试不同的输入方法和参数")
    print("=" * 50)
    
    # 配置浏览器选项
    co = ChromiumOptions()
    co.headless(False)  # 可视模式
    co.set_argument('--disable-blink-features=AutomationControlled')
    co.set_argument('--disable-dev-shm-usage')
    co.set_argument('--no-sandbox')
    co.set_user_agent('Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36')
    
    try:
        # 创建页面对象
        page = ChromiumPage(co)
        
        # 访问发帖页面
        target_url = 'https://086wow.cn/forum.php?mod=post&action=newthread&fid=2'
        print(f"🔍 访问发帖页面: {target_url}")
        
        page.get(target_url, retry=3, interval=2, timeout=15)
        time.sleep(5)
        
        print(f"📄 当前页面标题: {page.title}")
        print("-" * 50)
        
        # 找到输入框
        username_input = page.ele('xpath://input[starts-with(@id, "username_")]', timeout=5)
        password_input = page.ele('xpath://input[@type="password"]', timeout=5)
        
        if not username_input or not password_input:
            print("❌ 未找到输入框")
            return False
        
        print("✅ 找到输入框")
        print(f"用户名输入框ID: {username_input.attr('id')}")
        print(f"密码输入框ID: {password_input.attr('id')}")
        print("-" * 30)
        
        # 测试不同的输入方法
        test_methods = [
            ("方法1: input(text)", lambda elem, text: elem.input(text)),
            ("方法2: input(text, clear=True)", lambda elem, text: elem.input(text, clear=True)),
            ("方法3: clear() + input(text)", lambda elem, text: (elem.clear(), elem.input(text))),
            ("方法4: 直接设置value", lambda elem, text: page.run_js(f"arguments[0].value = '{text}';", elem)),
        ]
        
        test_username = "test_user_123"
        test_password = "test_pass_456"
        
        for i, (method_name, method_func) in enumerate(test_methods):
            print(f"🔍 测试 {method_name}:")
            
            try:
                # 测试用户名输入
                start_time = time.time()
                if "clear() +" in method_name:
                    username_input.clear()
                    username_input.input(test_username)
                else:
                    method_func(username_input, test_username)
                username_time = time.time() - start_time
                
                time.sleep(0.2)
                
                # 测试密码输入
                start_time = time.time()
                if "clear() +" in method_name:
                    password_input.clear()
                    password_input.input(test_password)
                else:
                    method_func(password_input, test_password)
                password_time = time.time() - start_time
                
                print(f"  ✅ 用户名输入耗时: {username_time:.2f}秒")
                print(f"  ✅ 密码输入耗时: {password_time:.2f}秒")
                print(f"  ✅ 总耗时: {username_time + password_time:.2f}秒")
                
                # 验证输入结果
                username_value = page.run_js("return arguments[0].value;", username_input)
                password_value = page.run_js("return arguments[0].value;", password_input)
                
                print(f"  用户名值: {username_value}")
                print(f"  密码值: {'*' * len(password_value) if password_value else '空'}")
                
                if username_value == test_username and password_value == test_password:
                    print(f"  ✅ 输入验证成功")
                else:
                    print(f"  ❌ 输入验证失败")
                
            except Exception as e:
                print(f"  ❌ 方法失败: {e}")
            
            print()
            time.sleep(1)  # 方法间间隔
        
        print("-" * 30)
        
        # 测试最快的输入组合
        print("🚀 测试最快输入组合:")
        try:
            start_time = time.time()
            
            # 清空
            username_input.clear()
            password_input.clear()
            
            # 快速输入
            username_input.input(test_username, clear=True)
            password_input.input(test_password, clear=True)
            
            total_time = time.time() - start_time
            print(f"✅ 最快输入总耗时: {total_time:.2f}秒")
            
        except Exception as e:
            print(f"❌ 最快输入失败: {e}")
        
        print("\n" + "=" * 50)
        print("测试完成！请查看浏览器窗口确认输入效果。")
        print("按回车键关闭浏览器...")
        input()
        
        page.quit()
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        try:
            page.quit()
        except:
            pass
        return False

def main():
    """主函数"""
    print("🔍 输入速度测试工具")
    print("此工具测试不同输入方法的速度和效果")
    print("=" * 50)
    
    # 检查依赖
    try:
        from DrissionPage import ChromiumPage, ChromiumOptions
        print("✅ DrissionPage 导入成功")
    except ImportError as e:
        print(f"❌ DrissionPage 导入失败: {e}")
        print("请先安装: pip install DrissionPage")
        return
    
    print("\n开始测试...")
    test_input_speed()

if __name__ == "__main__":
    main()
