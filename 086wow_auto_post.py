import requests
import re
import time
from bs4 import BeautifulSoup
import ddddocr
import json
import base64
from io import BytesIO
from PIL import Image

# 账号列表 (账号, 密码)
accounts = [
    ('WiWow01', '123456wb'),

]

# 目标URL和发帖参数
TARGET_URL = 'https://086wow.cn/forum.php?mod=post&action=newthread&fid=2'
FID = '2'  # 版块ID

# 发帖内容
POST_TITLE = '乌龟西格玛 —— 60级1.17乌龟魔爽单刷服'
POST_MESSAGE = """
乌龟西格玛 —— 1.17乌龟魔爽单刷首发【免费体验服】

特色玩法抢先看

加入我们，一起开荒！
点击链接加入QQ群：
http://qm.qq.com/cgi-bin/qm/qr?_wv=1027&k=xxx&group_code=*********

QQ群号：*********
QQ群号：*********
QQ群号：*********

参与宣传，领取奖励！
额
"""

headers = {
    'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
    'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
    'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
    'Accept-Encoding': 'gzip, deflate, br'
}

def solve_question(question):
    """简单四则运算自动答题"""
    # 匹配 "数字 运算符 数字 = ?" 格式，支持 +、-、*、/
    m = re.search(r"(\d+)\s*([\+\-\*\/×÷])\s*(\d+)\s*=\s*\?", question)
    if not m:
        return ""
    a, op, b = int(m.group(1)), m.group(2), int(m.group(3))
    
    if op == '+':
        return str(a + b)
    elif op == '-':
        return str(a - b)
    elif op in ['*', '×']:
        return str(a * b)
    elif op in ['/', '÷']:
        return str(a // b)  # 整数除法
    else:
        return ""

def handle_aliyun_slider_captcha(session, username):
    """处理阿里云滑块验证"""
    try:
        print(f"[{username}] � 开始处理阿里云滑块验证...")
        
        # 初始化ddddocr滑块识别
        det = ddddocr.DdddOcr(det=False, ocr=False, show_ad=False)
        
        # 获取滑块验证的初始化数据
        captcha_init_url = "https://captcha.086wow.cn/captcha/initialize"
        init_headers = {
            'Referer': 'https://086wow.cn/',
            'Accept': 'application/json',
            'Content-Type': 'application/json'
        }
        
        # 请求验证码初始化
        init_response = session.get(captcha_init_url, headers=init_headers, timeout=15)
        if init_response.status_code != 200:
            print(f"[{username}] ❌ 获取验证码初始化失败")
            return False
            
        init_data = init_response.json()
        session_id = init_data.get('sessionId', '')
        print(f"[{username}] 🔑 获取验证码会话ID: {session_id}")
        
        # 获取滑块验证图片
        captcha_image_url = f"https://captcha.086wow.cn/captcha/get?sessionId={session_id}"
        image_response = session.get(captcha_image_url, timeout=15)
        
        if image_response.status_code != 200:
            print(f"[{username}] ❌ 获取验证码图片失败")
            return False
            
        # 解析返回的验证码数据
        captcha_data = image_response.json()
        bg_image_b64 = captcha_data.get('bg', '')
        slide_image_b64 = captcha_data.get('slide', '')
        
        if not bg_image_b64 or not slide_image_b64:
            print(f"[{username}] ❌ 验证码图片数据不完整")
            return False
            
        # 解码base64图片
        bg_image_data = base64.b64decode(bg_image_b64)
        slide_image_data = base64.b64decode(slide_image_b64)
        
        print(f"[{username}] �️ 获取验证码图片成功")
        
        # 使用ddddocr识别滑块位置
        result = det.slide_match(slide_image_data, bg_image_data, simple_target=True)
        slide_x = result['target'][0]
        
        print(f"[{username}] 🎯 识别滑块位置: x={slide_x}")
        
        # 生成滑动轨迹
        def generate_slide_track(distance):
            """生成滑动轨迹"""
            track = []
            current = 0
            mid = distance * 4 / 5
            t = 0.2
            v = 0
            
            while current < distance:
                if current < mid:
                    a = 2
                else:
                    a = -3
                v0 = v
                v = v0 + a * t
                move = v0 * t + 1 / 2 * a * t * t
                current += move
                track.append(round(move))
            return track
        
        track = generate_slide_track(slide_x)
        
        # 提交滑块验证结果
        verify_url = f"https://captcha.086wow.cn/captcha/verify"
        verify_data = {
            'sessionId': session_id,
            'x': slide_x,
            'track': track
        }
        
        verify_response = session.post(verify_url, json=verify_data, headers=init_headers, timeout=15)
        
        if verify_response.status_code == 200:
            verify_result = verify_response.json()
            if verify_result.get('success', False):
                print(f"[{username}] ✅ 滑块验证成功！")
                return session_id
            else:
                print(f"[{username}] ❌ 滑块验证失败: {verify_result.get('message', '未知错误')}")
                return False
        else:
            print(f"[{username}] ❌ 提交滑块验证失败")
            return False
            
    except Exception as e:
        print(f"[{username}] ❌ 处理滑块验证时出错: {e}")
        return False

def enhanced_login_to_086wow(username, password):
    """增强版登录函数，支持滑块验证"""
    session = requests.Session()
    session.headers.update(headers)

    print(f"[{username}] 🚀 开始增强版登录流程...")

    # Step 1: 获取登录页面
    try:
        login_page_url = 'https://086wow.cn/member.php?mod=logging&action=login'
        res_login_page = session.get(login_page_url, timeout=15)
        print(f"[{username}] 📄 获取登录页面成功")
    except Exception as e:
        print(f"[{username}] ❌ 无法连接登录页: {e}")
        return None

    # 解析登录页面
    soup = BeautifulSoup(res_login_page.text, "html.parser")
    
    # 获取必要参数
    login_hash_match = re.search(r"loginhash=(\w+)", res_login_page.text)
    if not login_hash_match:
        print(f"[{username}] ❌ 获取 loginhash 失败")
        return None
    
    login_hash = login_hash_match.group(1)
    print(f"[{username}] 🔑 获取 loginhash: {login_hash}")

    formhash_tag = soup.find("input", attrs={"name": "formhash"})
    formhash = formhash_tag.get("value", "") if formhash_tag else ""

    # Step 2: 处理数学验证码
    secqaa_hash = None
    secanswer = None
    
    secqaa_match = re.search(r"secqaa_(\w+)", res_login_page.text)
    if secqaa_match:
        secqaa_hash = secqaa_match.group(1)
        print(f"[{username}] 🔍 找到数学验证码hash: {secqaa_hash}")

        secqaa_url = f"https://086wow.cn/misc.php?mod=secqaa&action=update&idhash={secqaa_hash}&inajax=1"
        secqaa_response = session.get(secqaa_url, timeout=15)
        
        question_match = re.search(r'(\d+\s*[\+\-\*\/×÷]\s*\d+\s*=\s*\?)', secqaa_response.text)
        if question_match:
            question_text = question_match.group(1)
            secanswer = solve_question(question_text)
            print(f"[{username}] 🔍 数学问题: {question_text}, 答案: {secanswer}")

    # Step 3: 第一次登录尝试
    login_url = f"https://086wow.cn/member.php?mod=logging&action=login&loginsubmit=yes&loginhash={login_hash}&inajax=1"
    
    login_data = {
        'username': username,
        'password': password,
        'loginsubmit': 'yes',
        'cookietime': '2592000'
    }
    
    if formhash:
        login_data['formhash'] = formhash
    if secqaa_hash and secanswer:
        login_data['secqaahash'] = secqaa_hash
        login_data['secanswer'] = secanswer

    try:
        res_login = session.post(login_url, data=login_data, timeout=15)
        print(f"[{username}] � 第一次登录请求已发送")
    except Exception as e:
        print(f"[{username}] ❌ 登录请求失败: {e}")
        return None

    # Step 4: 检查是否需要滑块验证
    if '智能验证失败' in res_login.text or 'aliyunCaptcha' in res_login.text:
        print(f"[{username}] 🔍 检测到需要滑块验证")
        
        # 处理滑块验证
        captcha_session_id = handle_aliyun_slider_captcha(session, username)
        if not captcha_session_id:
            return None
            
        # 带着滑块验证结果重新登录
        login_data['captcha_session_id'] = captcha_session_id
        
        try:
            res_login = session.post(login_url, data=login_data, timeout=15)
            print(f"[{username}] 📤 带滑块验证的登录请求已发送")
        except Exception as e:
            print(f"[{username}] ❌ 滑块验证登录请求失败: {e}")
            return None

    # Step 5: 检查最终登录结果
    success_indicators = ['欢迎您回来', 'window.location.href', 'succeedhandle_', '登录成功']
    
    if any(indicator in res_login.text for indicator in success_indicators):
        print(f"[{username}] ✅ 登录成功！")
        return session
    else:
        print(f"[{username}] ❌ 登录失败")
        print(f"[{username}] 响应内容: {res_login.text[:500]}")
        return None

def check_login_status(session, username):
    """检查登录状态"""
    try:
        print(f"[{username}] 🔍 检查登录状态...")
        # 访问个人中心页面来验证登录状态
        profile_url = "https://086wow.cn/home.php?mod=space&uid=1"
        res = session.get(profile_url, timeout=15)

        if '退出' in res.text or 'logout' in res.text or username in res.text:
            print(f"[{username}] ✅ 登录状态有效")
            return True
        else:
            print(f"[{username}] ❌ 登录状态已失效")
            return False
    except Exception as e:
        print(f"[{username}] ❌ 检查登录状态失败: {e}")
        return False

def access_target_page(session, username):
    """访问目标页面"""
    try:
        print(f"[{username}] 🎯 正在访问目标页面...")
        res = session.get(TARGET_URL, timeout=15)

        # 检查页面内容，判断是否能正常访问发帖页面
        if '发表新主题' in res.text or '发帖' in res.text or 'newthread' in res.text:
            print(f"[{username}] ✅ 成功访问发帖页面！")
            return True, res
        elif '尚未登录' in res.text or '没有权限' in res.text or 'login' in res.text:
            print(f"[{username}] ❌ 仍需要登录，可能登录状态已失效")
            print(f"[{username}] 页面内容片段: {res.text[:500]}")
            return False, None
        else:
            print(f"[{username}] ⚠️ 页面状态未知")
            print(f"[{username}] 页面内容片段: {res.text[:500]}")
            return False, None

    except Exception as e:
        print(f"[{username}] ❌ 访问目标页面失败: {e}")
        return False, None

def post_thread(session, username, post_page_response):
    """发帖功能"""
    print(f"[{username}] 📝 开始发帖流程...")

    try:
        # 解析发帖页面，获取必要参数
        soup = BeautifulSoup(post_page_response.text, "html.parser")

        # 获取 formhash
        formhash_tag = soup.find("input", attrs={"name": "formhash"})
        if not formhash_tag:
            print(f"[{username}] ❌ 获取发帖页面 formhash 失败")
            return False
        formhash = formhash_tag.get("value", "")
        print(f"[{username}] 🔑 获取发帖 formhash: {formhash}")

        # 检查验证问答
        secqaa_hash = None
        secanswer = None

        # 从JavaScript中提取hash值
        secqaa_match = re.search(r"secqaa_(\w+)", post_page_response.text)
        if secqaa_match:
            secqaa_hash = secqaa_match.group(1)
            print(f"[{username}] 🔍 找到发帖验证问答hash: {secqaa_hash}")

            # 请求验证问答内容
            secqaa_url = f"https://086wow.cn/misc.php?mod=secqaa&action=update&idhash={secqaa_hash}&inajax=1"
            secqaa_headers = {
                'Referer': TARGET_URL,
                'X-Requested-With': 'XMLHttpRequest',
                'Accept': '*/*'
            }
            try:
                secqaa_response = session.get(secqaa_url, headers=secqaa_headers, timeout=15)
                print(f"[{username}] 🔍 发帖验证问答响应: {secqaa_response.text[:200]}")

                # 解析验证问答内容
                response_text = secqaa_response.text
                question_match = re.search(r'(\d+\s*[\+\-\*\/×÷]\s*\d+\s*=\s*\?)', response_text)
                if question_match:
                    question_text = question_match.group(1)
                    secanswer = solve_question(question_text)
                    print(f"[{username}] 🔍 发帖问题: {question_text}, 答案: {secanswer}")
                else:
                    # 尝试从HTML中解析
                    if '<' in response_text and '>' in response_text:
                        soup_secqaa = BeautifulSoup(response_text, "html.parser")
                        question_text = soup_secqaa.get_text()
                        question_match = re.search(r'(\d+\s*[\+\-\*\/×÷]\s*\d+\s*=\s*\?)', question_text)
                        if question_match:
                            question_text = question_match.group(1)
                            secanswer = solve_question(question_text)
                            print(f"[{username}] 🔍 从HTML解析发帖问题: {question_text}, 答案: {secanswer}")
            except Exception as e:
                print(f"[{username}] ❌ 获取发帖验证问答失败: {e}")
        else:
            # 尝试直接从页面中查找验证问答
            page_question_match = re.search(r'(\d+\s*[\+\-\*\/×÷]\s*\d+\s*=\s*\?)', post_page_response.text)
            if page_question_match:
                question_text = page_question_match.group(1)
                secanswer = solve_question(question_text)
                print(f"[{username}] 🔍 从发帖页面直接找到问题: {question_text}, 答案: {secanswer}")

        # 构建发帖数据
        post_data = {
            'formhash': formhash,
            'subject': POST_TITLE,
            'message': POST_MESSAGE,
            'topicsubmit': 'yes'
        }

        if secqaa_hash and secanswer:
            post_data['secqaahash'] = secqaa_hash
            post_data['secanswer'] = secanswer

        print(f"[{username}] 🔍 发帖数据: {post_data}")

        # 执行发帖 - 添加更多必要的headers
        post_submit_url = f"https://086wow.cn/forum.php?mod=post&action=newthread&fid={FID}&topicsubmit=yes&infloat=yes&inajax=1"

        # 设置发帖请求的headers
        post_headers = {
            'Content-Type': 'application/x-www-form-urlencoded',
            'Referer': TARGET_URL,
            'X-Requested-With': 'XMLHttpRequest',
            'Origin': 'https://086wow.cn'
        }

        try:
            res_post = session.post(post_submit_url, data=post_data, headers=post_headers, timeout=15)
            print(f"[{username}] 📤 发帖请求已发送")
        except Exception as e:
            print(f"[{username}] ❌ 发帖请求超时: {e}")
            return False

        # 检查发帖是否成功
        success_indicators = [
            '回复',
            'window.location.href',
            'succeedhandle_',
            '非常感谢，您的主题已发布',
            '现在将转入主题页',
            'tid='  # 主题ID出现表示成功
        ]

        if any(indicator in res_post.text for indicator in success_indicators):
            # 尝试提取主题ID
            tid_match = re.search(r"tid['\"]?[:=]['\"]?(\d+)", res_post.text)
            if tid_match:
                tid = tid_match.group(1)
                print(f"[{username}] ✅ 发帖成功！主题ID: {tid}")
            else:
                print(f"[{username}] ✅ 发帖成功")
            return True
        else:
            print(f"[{username}] ❌ 发帖失败，返回内容如下：\n{res_post.text[:500]}")
            return False

    except Exception as e:
        print(f"[{username}] ❌ 发帖过程中出现异常: {e}")
        return False

def login_and_post_single(username, password):
    """单个账号的登录和发帖流程 - 使用增强版登录"""
    print(f"\n➡️ 开始处理账号 {username}...")

    # 使用增强版登录
    session = enhanced_login_to_086wow(username, password)

    if session:
        # 检查登录状态
        if not check_login_status(session, username):
            print(f"[{username}] ❌ 登录状态验证失败")
            return False

        time.sleep(2)

        # 访问目标页面
        success, post_page_response = access_target_page(session, username)

        if success:
            print(f"[{username}] ✅ 登录成功并可以访问发帖页面")

            # 执行发帖
            post_success = post_thread(session, username, post_page_response)
            if post_success:
                print(f"[{username}] 🎉 发帖成功！")
                return True
            else:
                print(f"[{username}] ❌ 发帖失败")
                return False
        else:
            print(f"[{username}] ❌ 虽然登录成功，但无法正常访问目标页面")
            return False
    else:
        print(f"[{username}] ❌ 登录失败")
        return False

def main():
    """主函数 - 6个账号一起发帖"""
    print("🚀 开始 086wow.cn 6账号自动发帖程序")
    print(f"📋 账号数量: {len(accounts)}")
    print(f"🎯 目标页面: {TARGET_URL}")
    print(f"📝 发帖标题: {POST_TITLE}")
    print("-" * 50)

    # 询问用户操作模式
    while True:
        print("\n请选择操作模式:")
        print("1. 立即执行一轮发帖（6个账号各发一次）")
        print("2. 循环发帖模式（每21分钟一轮，无限循环）")
        print("3. 退出程序")

        user_choice = input("请输入选择 (1/2/3): ").strip()

        if user_choice == '1':
            # 单轮发帖
            print(f"\n🚩 开始单轮发帖 —— 当前时间：{time.strftime('%Y-%m-%d %H:%M:%S')}")
            success_count = 0

            for username, password in accounts:
                result = login_and_post_single(username, password)
                if result:
                    success_count += 1
                time.sleep(3)  # 账号间隔3秒

            print(f"\n✅ 单轮发帖完成！成功: {success_count}/{len(accounts)}")

        elif user_choice == '2':
            # 循环发帖模式
            print(f"\n🔄 进入循环发帖模式...")
            round_count = 0

            try:
                while True:
                    round_count += 1
                    print(f"\n🚩 第 {round_count} 轮发帖开始 —— 当前时间：{time.strftime('%Y-%m-%d %H:%M:%S')}")
                    success_count = 0

                    for username, password in accounts:
                        result = login_and_post_single(username, password)
                        if result:
                            success_count += 1
                        time.sleep(3)  # 账号间隔3秒

                    print(f"\n✅ 第 {round_count} 轮发帖完毕！成功: {success_count}/{len(accounts)}")
                    print(f"💤 休息 21 分钟后开始下一轮...")
                    time.sleep(21 * 60)  # 休息21分钟

            except KeyboardInterrupt:
                print(f"\n\n⏹️ 用户中断程序，已完成 {round_count} 轮发帖")
                break

        elif user_choice == '3':
            print(f"\n👋 程序退出")
            break

        else:
            print(f"\n❌ 无效选择，请输入 1、2 或 3")

if __name__ == "__main__":
    main()

