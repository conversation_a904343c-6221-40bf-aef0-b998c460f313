#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试特定元素 - 测试你提供的具体登录元素
"""

from DrissionPage import ChromiumPage, ChromiumOptions
import time

def test_specific_elements():
    """测试特定的登录元素"""
    print("🚀 测试特定登录元素")
    print("测试元素:")
    print('- <input type="text" name="username" id="username_Lu78x" autocomplete="off" size="30" class="px p_fre" value="">')
    print('- <input type="password" id="password3_Lu78x" name="password" onfocus="clearpwd()" size="30" class="px p_fre">')
    print('- <button class="pn pnc" type="submit" name="loginsubmit" value="true"><strong>登录</strong></button>')
    print("=" * 80)
    
    # 配置浏览器选项
    co = ChromiumOptions()
    co.headless(False)  # 可视模式
    co.set_argument('--disable-blink-features=AutomationControlled')
    co.set_argument('--disable-dev-shm-usage')
    co.set_argument('--no-sandbox')
    co.set_user_agent('Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36')
    
    try:
        # 创建页面对象
        page = ChromiumPage(co)
        
        # 访问发帖页面
        target_url = 'https://086wow.cn/forum.php?mod=post&action=newthread&fid=2'
        print(f"🔍 访问发帖页面: {target_url}")
        
        page.get(target_url, retry=3, interval=2, timeout=15)
        time.sleep(5)  # 等待页面完全加载
        
        print(f"📄 当前页面标题: {page.title}")
        print(f"📄 当前页面URL: {page.url}")
        print("-" * 50)
        
        # 测试具体的用户名输入框
        print("🔍 测试用户名输入框:")
        username_tests = [
            ('按ID查找', 'xpath://input[@id="username_Lu78x"]'),
            ('按name查找', 'xpath://input[@name="username"]'),
            ('按class查找', 'xpath://input[@class="px p_fre" and @type="text"]'),
            ('组合查找', 'xpath://input[@name="username" and @id="username_Lu78x"]')
        ]
        
        username_element = None
        for test_name, selector in username_tests:
            element = page.ele(selector, timeout=3)
            if element:
                print(f"  ✅ {test_name}: 找到")
                print(f"     完整HTML: {element.html}")
                username_element = element
                break
            else:
                print(f"  ❌ {test_name}: 未找到")
        
        print("-" * 30)
        
        # 测试具体的密码输入框
        print("🔍 测试密码输入框:")
        password_tests = [
            ('按ID查找', 'xpath://input[@id="password3_Lu78x"]'),
            ('按name查找', 'xpath://input[@name="password"]'),
            ('按type查找', 'xpath://input[@type="password"]'),
            ('组合查找', 'xpath://input[@name="password" and @id="password3_Lu78x"]')
        ]
        
        password_element = None
        for test_name, selector in password_tests:
            element = page.ele(selector, timeout=3)
            if element:
                print(f"  ✅ {test_name}: 找到")
                print(f"     完整HTML: {element.html}")
                password_element = element
                break
            else:
                print(f"  ❌ {test_name}: 未找到")
        
        print("-" * 30)
        
        # 测试具体的登录按钮
        print("🔍 测试登录按钮:")
        login_tests = [
            ('按name查找', 'xpath://button[@name="loginsubmit"]'),
            ('按class查找', 'xpath://button[@class="pn pnc"]'),
            ('按value查找', 'xpath://button[@value="true"]'),
            ('按文本查找', 'xpath://button[contains(text(), "登录")]'),
            ('组合查找', 'xpath://button[@name="loginsubmit" and @value="true"]')
        ]
        
        login_element = None
        for test_name, selector in login_tests:
            element = page.ele(selector, timeout=3)
            if element:
                print(f"  ✅ {test_name}: 找到")
                print(f"     完整HTML: {element.html}")
                login_element = element
                break
            else:
                print(f"  ❌ {test_name}: 未找到")
        
        print("-" * 50)
        
        # 尝试模拟登录过程（不输入真实账号密码）
        if username_element and password_element and login_element:
            print("🎉 所有登录元素都找到了！")
            print("\n🔍 测试元素交互:")
            
            try:
                # 测试用户名输入框
                print("  测试用户名输入框...")
                username_element.clear()
                username_element.input("test_user")
                print("  ✅ 用户名输入框可以正常输入")
                
                # 测试密码输入框
                print("  测试密码输入框...")
                password_element.clear()
                password_element.input("test_pass")
                print("  ✅ 密码输入框可以正常输入")
                
                print("  ✅ 所有表单元素都可以正常交互")
                print("  ⚠️ 注意：这只是测试，没有真正提交登录")
                
            except Exception as e:
                print(f"  ❌ 元素交互测试失败: {e}")
        else:
            print("❌ 部分登录元素未找到")
            print(f"  用户名输入框: {'✅' if username_element else '❌'}")
            print(f"  密码输入框: {'✅' if password_element else '❌'}")
            print(f"  登录按钮: {'✅' if login_element else '❌'}")
        
        print("\n" + "=" * 50)
        print("测试完成！请查看浏览器窗口确认页面状态。")
        print("按回车键关闭浏览器...")
        input()
        
        page.quit()
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        try:
            page.quit()
        except:
            pass
        return False

def main():
    """主函数"""
    print("🔍 特定登录元素测试工具")
    print("此工具专门测试你提供的具体登录元素")
    print("=" * 50)
    
    # 检查依赖
    try:
        from DrissionPage import ChromiumPage, ChromiumOptions
        print("✅ DrissionPage 导入成功")
    except ImportError as e:
        print(f"❌ DrissionPage 导入失败: {e}")
        print("请先安装: pip install DrissionPage")
        return
    
    print("\n开始测试...")
    test_specific_elements()

if __name__ == "__main__":
    main()
