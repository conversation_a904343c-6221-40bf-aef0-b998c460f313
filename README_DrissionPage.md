# 086wow.cn DrissionPage自动发帖程序

## 📋 项目简介

这是一个使用DrissionPage库开发的086wow.cn论坛自动发帖程序，相比原来的requests方案，DrissionPage具有以下优势：

### 🎯 DrissionPage的优势

1. **更好的验证码处理**：能够模拟真实的鼠标操作，通过滑块验证码
2. **更强的反爬能力**：模拟真实浏览器行为，不容易被检测
3. **更稳定的登录**：支持复杂的JavaScript渲染页面
4. **更灵活的操作**：可以处理各种复杂的页面交互

## 🛠️ 安装依赖

### 方法一：自动安装（推荐）
```bash
python install_dependencies.py
```

### 方法二：手动安装
```bash
pip install DrissionPage
pip install ddddocr
pip install Pillow
pip install opencv-python
```

## 📦 环境要求

- **Python**: 3.6 或更高版本
- **操作系统**: Windows、Linux、Mac
- **浏览器**: Chrome 或 Edge（Chromium内核）
- **网络**: 稳定的网络连接

## 🚀 使用方法

### 1. 配置账号信息
编辑 `086wow_drissionpage_auto_post.py` 文件中的账号列表：

```python
accounts = [
    ('账号1', '密码1'),
    ('账号2', '密码2'),
    # 添加更多账号...
]
```

### 2. 配置发帖内容
修改发帖标题和内容：

```python
POST_TITLE = '你的帖子标题'
POST_MESSAGE = """
你的帖子内容
可以是多行文本
"""
```

### 3. 测试登录流程（可选）
```bash
python test_login_flow.py
```

### 4. 运行程序
```bash
python 086wow_drissionpage_auto_post.py
```

### 5. 选择运行模式
程序启动后会提供三个选项：
- **1**: 立即执行一轮发帖
- **2**: 循环发帖模式（每21分钟一轮）
- **3**: 退出程序

## 🔧 功能特性

### ✅ 智能登录流程
- **直接访问发帖页面**：程序直接访问发帖URL，如果未登录会自动跳转到登录页面
- **自动填写表单**：自动填写用户名和密码
- **智能验证码处理**：自动处理数学验证码和滑块验证码
- **无缝跳转**：登录成功后自动跳转回发帖页面，无需额外操作

### ✅ 智能发帖
- 自动填写帖子标题和内容
- 支持富文本编辑器和普通文本框
- 自动处理发帖验证码
- 发帖结果验证

### ✅ 验证码处理
- **数学验证码**：自动计算四则运算
- **滑块验证码**：使用ddddocr识别 + 模拟人工滑动轨迹

### ✅ 反检测机制
- 模拟真实浏览器行为
- 随机化操作时间间隔
- 人性化鼠标轨迹
- 自定义User-Agent

## 🎮 滑块验证码处理原理

### 1. 图像识别
使用ddddocr库识别滑块缺口位置：
```python
det = ddddocr.DdddOcr(det=False, ocr=False, show_ad=False)
result = det.slide_match(slide_bytes, bg_bytes, simple_target=True)
offset = result['target'][0]
```

### 2. 轨迹生成
生成符合人类操作习惯的滑动轨迹：
- 前段快速移动
- 后段减速调整
- 添加随机抖动

### 3. 模拟操作
使用DrissionPage的动作链模拟真实滑动：
```python
page.actions.hold(slider_element)  # 按住滑块
for track in tracks:
    page.actions.move(offset_x=track, offset_y=random_y, duration=0.1)
page.actions.release()  # 释放滑块
```

## ⚙️ 配置选项

### 浏览器配置
```python
co = ChromiumOptions()
co.headless(False)  # 是否无头模式
co.set_argument('--disable-blink-features=AutomationControlled')
co.set_user_agent('自定义User-Agent')
```

### 时间间隔配置
- 账号间隔：5秒
- 轮次间隔：21分钟
- 操作间隔：1-3秒随机

## 🐛 常见问题

### Q1: 提示"未找到Chrome浏览器"
**解决方案**：
1. 安装Chrome浏览器
2. 或者在代码中指定浏览器路径：
```python
co.set_browser_path(r'C:\Program Files\Google\Chrome\Application\chrome.exe')
```

### Q2: 滑块验证码识别失败
**解决方案**：
1. 检查ddddocr是否正确安装
2. 尝试多次重试（程序已内置重试机制）
3. 检查网络连接是否稳定

### Q3: 登录失败
**解决方案**：
1. 检查账号密码是否正确
2. 检查网站是否有新的验证机制
3. 尝试手动登录一次确认账号状态

### Q4: 发帖失败
**解决方案**：
1. 检查账号是否有发帖权限
2. 检查发帖内容是否符合论坛规则
3. 检查版块ID是否正确

## 📝 更新日志

### v2.0.0 (DrissionPage版本)
- ✅ 使用DrissionPage替代requests
- ✅ 改进滑块验证码处理
- ✅ 增强反检测能力
- ✅ 优化用户体验

### v1.0.0 (requests版本)
- ✅ 基础登录和发帖功能
- ✅ 数学验证码处理
- ✅ 基础滑块验证码处理

## 🤝 贡献

欢迎提交Issue和Pull Request来改进这个项目！

## ⚠️ 免责声明

本程序仅供学习和研究使用，请遵守相关网站的使用条款和法律法规。使用本程序所产生的任何后果由使用者自行承担。

## 📞 支持

如果遇到问题，可以：
1. 查看本文档的常见问题部分
2. 提交GitHub Issue
3. 参考DrissionPage官方文档：https://www.drissionpage.cn/

---

**祝你使用愉快！** 🎉
