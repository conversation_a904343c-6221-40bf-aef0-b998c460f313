#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试登录按钮 - 专门测试登录按钮的点击
"""

from DrissionPage import ChromiumPage, ChromiumOptions
import time

def test_login_button():
    """测试登录按钮"""
    print("🚀 测试登录按钮")
    print("目标按钮: <button type=\"submit\" class=\"pn vm\" style=\"width: 75px;\"><em>登录</em></button>")
    print("=" * 70)
    
    # 配置浏览器选项
    co = ChromiumOptions()
    co.headless(False)  # 可视模式
    co.set_argument('--disable-blink-features=AutomationControlled')
    co.set_argument('--disable-dev-shm-usage')
    co.set_argument('--no-sandbox')
    co.set_user_agent('Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36')
    
    try:
        # 创建页面对象
        page = ChromiumPage(co)
        
        # 访问发帖页面
        target_url = 'https://086wow.cn/forum.php?mod=post&action=newthread&fid=2'
        print(f"🔍 访问发帖页面: {target_url}")
        
        page.get(target_url, retry=3, interval=2, timeout=15)
        time.sleep(5)  # 等待页面完全加载
        
        print(f"📄 当前页面标题: {page.title}")
        print(f"📄 当前页面URL: {page.url}")
        print("-" * 50)
        
        # 查找所有按钮
        print("🔍 查找页面中的所有按钮:")
        all_buttons = page.eles('xpath://button')
        
        login_buttons = []
        
        for i, button in enumerate(all_buttons):
            button_type = button.attr('type') or ''
            button_class = button.attr('class') or ''
            button_style = button.attr('style') or ''
            button_text = button.text or ''
            button_html = button.html or ''
            
            print(f"  按钮 {i+1}:")
            print(f"    Type: {button_type}")
            print(f"    Class: {button_class}")
            print(f"    Style: {button_style}")
            print(f"    Text: {button_text}")
            print(f"    HTML: {button_html[:100]}...")
            
            # 检查是否是登录按钮
            if ('登录' in button_text or '登录' in button_html or 
                ('submit' in button_type and ('pn' in button_class or 'vm' in button_class))):
                login_buttons.append(button)
                print(f"    >>> 🎯 这可能是登录按钮!")
            
            print()
        
        print("-" * 30)
        
        # 测试各种登录按钮选择器
        print("🔍 测试登录按钮选择器:")
        button_selectors = [
            ('type=submit + class=pn', 'xpath://button[@type="submit" and contains(@class, "pn")]'),
            ('type=submit + class=vm', 'xpath://button[@type="submit" and contains(@class, "vm")]'),
            ('class=pn vm', 'xpath://button[contains(@class, "pn vm")]'),
            ('class=pn + class=vm', 'xpath://button[contains(@class, "pn") and contains(@class, "vm")]'),
            ('包含登录文本', 'xpath://button[contains(., "登录")]'),
            ('包含em登录', 'xpath://button[contains(., "登录")]/em | xpath://button//em[contains(text(), "登录")]'),
            ('width=75px', 'xpath://button[contains(@style, "width: 75px")]'),
            ('submit+登录', 'xpath://button[@type="submit" and contains(., "登录")]')
        ]
        
        found_buttons = []
        for test_name, selector in button_selectors:
            element = page.ele(selector, timeout=2)
            if element:
                print(f"  ✅ {test_name}: 找到")
                print(f"     HTML: {element.html}")
                found_buttons.append((test_name, element))
            else:
                print(f"  ❌ {test_name}: 未找到")
        
        print("-" * 30)
        
        # 测试按钮点击
        if found_buttons:
            print("🔍 测试按钮点击:")
            
            # 先填写一些测试数据（避免空表单提交）
            username_input = page.ele('xpath://input[starts-with(@id, "username_")]', timeout=3)
            password_input = page.ele('xpath://input[@type="password"]', timeout=3)
            
            if username_input and password_input:
                print("  填写测试数据...")
                username_input.input("test_user")
                password_input.input("test_pass")
                time.sleep(1)
            
            # 测试第一个找到的按钮
            test_name, test_button = found_buttons[0]
            print(f"  测试按钮: {test_name}")
            
            try:
                print("  尝试普通点击...")
                test_button.click()
                time.sleep(3)
                print("  ✅ 普通点击成功")
                
                # 检查页面是否有变化
                new_url = page.url
                new_title = page.title
                print(f"  点击后URL: {new_url}")
                print(f"  点击后标题: {new_title}")
                
            except Exception as e:
                print(f"  ❌ 普通点击失败: {e}")
                
                try:
                    print("  尝试JavaScript点击...")
                    page.run_js("arguments[0].click();", test_button)
                    time.sleep(3)
                    print("  ✅ JavaScript点击成功")
                    
                    # 检查页面是否有变化
                    new_url = page.url
                    new_title = page.title
                    print(f"  点击后URL: {new_url}")
                    print(f"  点击后标题: {new_title}")
                    
                except Exception as e2:
                    print(f"  ❌ JavaScript点击失败: {e2}")
                    
                    try:
                        print("  尝试表单提交...")
                        form = page.ele('xpath://form', timeout=3)
                        if form:
                            form.submit()
                            time.sleep(3)
                            print("  ✅ 表单提交成功")
                            
                            # 检查页面是否有变化
                            new_url = page.url
                            new_title = page.title
                            print(f"  提交后URL: {new_url}")
                            print(f"  提交后标题: {new_title}")
                        else:
                            print("  ❌ 未找到表单")
                    except Exception as e3:
                        print(f"  ❌ 表单提交失败: {e3}")
        else:
            print("❌ 未找到任何登录按钮")
        
        print("\n" + "=" * 50)
        print("测试完成！请查看浏览器窗口确认页面状态。")
        print("按回车键关闭浏览器...")
        input()
        
        page.quit()
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        try:
            page.quit()
        except:
            pass
        return False

def main():
    """主函数"""
    print("🔍 登录按钮测试工具")
    print("此工具专门测试登录按钮的识别和点击")
    print("=" * 50)
    
    # 检查依赖
    try:
        from DrissionPage import ChromiumPage, ChromiumOptions
        print("✅ DrissionPage 导入成功")
    except ImportError as e:
        print(f"❌ DrissionPage 导入失败: {e}")
        print("请先安装: pip install DrissionPage")
        return
    
    print("\n开始测试...")
    test_login_button()

if __name__ == "__main__":
    main()
