#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试DrissionPage环境是否正常
"""

import sys
import time

def test_imports():
    """测试导入"""
    print("🔍 测试依赖包导入...")
    
    try:
        from DrissionPage import ChromiumPage, ChromiumOptions
        print("✅ DrissionPage 导入成功")
    except ImportError as e:
        print(f"❌ DrissionPage 导入失败: {e}")
        return False
    
    try:
        import ddddocr
        print("✅ ddddocr 导入成功")
    except ImportError as e:
        print(f"❌ ddddocr 导入失败: {e}")
        return False
    
    try:
        from PIL import Image
        print("✅ Pillow 导入成功")
    except ImportError as e:
        print(f"❌ Pillow 导入失败: {e}")
        return False
    
    return True

def test_ddddocr():
    """测试ddddocr功能"""
    print("\n🔍 测试ddddocr滑块识别功能...")
    
    try:
        import ddddocr
        det = ddddocr.DdddOcr(det=False, ocr=False, show_ad=False)
        print("✅ ddddocr滑块识别器初始化成功")
        return True
    except Exception as e:
        print(f"❌ ddddocr滑块识别器初始化失败: {e}")
        return False

def test_browser():
    """测试浏览器启动"""
    print("\n🔍 测试浏览器启动...")
    
    try:
        from DrissionPage import ChromiumPage, ChromiumOptions
        
        # 配置浏览器选项
        co = ChromiumOptions()
        co.headless(True)  # 无头模式测试
        co.set_argument('--disable-blink-features=AutomationControlled')
        co.set_argument('--disable-dev-shm-usage')
        co.set_argument('--no-sandbox')
        
        # 创建页面对象
        page = ChromiumPage(co)
        print("✅ 浏览器启动成功")
        
        # 测试访问网页
        print("🔍 测试访问百度...")
        page.get('https://www.baidu.com', timeout=10)
        
        if '百度' in page.title:
            print("✅ 网页访问成功")
        else:
            print("⚠️ 网页访问异常")
        
        # 关闭浏览器
        page.quit()
        print("✅ 浏览器关闭成功")
        return True
        
    except Exception as e:
        print(f"❌ 浏览器测试失败: {e}")
        try:
            page.quit()
        except:
            pass
        return False

def test_target_site():
    """测试目标网站访问"""
    print("\n🔍 测试086wow.cn网站访问...")
    
    try:
        from DrissionPage import ChromiumPage, ChromiumOptions
        
        co = ChromiumOptions()
        co.headless(True)
        co.set_argument('--disable-blink-features=AutomationControlled')
        co.set_argument('--disable-dev-shm-usage')
        co.set_argument('--no-sandbox')
        co.set_user_agent('Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36')
        
        page = ChromiumPage(co)
        
        # 访问发帖页面（会自动跳转到登录）
        login_url = 'https://086wow.cn/forum.php?mod=post&action=newthread&fid=2'
        print(f"🔍 访问发帖页面（会跳转到登录）: {login_url}")

        page.get(login_url, timeout=15)
        time.sleep(3)

        if '登录' in page.html or 'login' in page.html or '发表' in page.html:
            print("✅ 页面访问成功（登录或发帖页面）")
        else:
            print("⚠️ 页面访问异常")
        
        # 访问发帖页面
        post_url = 'https://086wow.cn/forum.php?mod=post&action=newthread&fid=2'
        print(f"🔍 访问发帖页面: {post_url}")
        
        page.get(post_url, timeout=15)
        time.sleep(3)
        
        if '发表' in page.html or 'newthread' in page.html or '登录' in page.html:
            print("✅ 发帖页面访问成功")
        else:
            print("⚠️ 发帖页面访问异常")
        
        page.quit()
        return True
        
    except Exception as e:
        print(f"❌ 目标网站访问测试失败: {e}")
        try:
            page.quit()
        except:
            pass
        return False

def main():
    """主函数"""
    print("🚀 DrissionPage环境测试")
    print("=" * 50)
    
    # 检查Python版本
    python_version = sys.version_info
    print(f"Python版本: {python_version.major}.{python_version.minor}.{python_version.micro}")
    
    if python_version.major < 3 or (python_version.major == 3 and python_version.minor < 6):
        print("❌ Python版本过低，需要Python 3.6或更高版本")
        return
    
    print("✅ Python版本检查通过")
    print("-" * 50)
    
    # 测试步骤
    tests = [
        ("依赖包导入", test_imports),
        ("ddddocr功能", test_ddddocr),
        ("浏览器启动", test_browser),
        ("目标网站访问", test_target_site),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n{'='*20} {test_name} {'='*20}")
        try:
            if test_func():
                passed += 1
                print(f"✅ {test_name} 测试通过")
            else:
                print(f"❌ {test_name} 测试失败")
        except Exception as e:
            print(f"❌ {test_name} 测试异常: {e}")
        
        time.sleep(1)
    
    # 总结
    print("\n" + "=" * 50)
    print(f"测试完成！通过: {passed}/{total}")
    
    if passed == total:
        print("🎉 所有测试通过！环境配置正常，可以运行自动发帖程序。")
        print("\n下一步:")
        print("1. 编辑 086wow_drissionpage_auto_post.py 配置账号信息")
        print("2. 运行 python 086wow_drissionpage_auto_post.py")
    else:
        print("⚠️ 部分测试失败，请检查环境配置。")
        print("\n建议:")
        print("1. 运行 python install_dependencies.py 重新安装依赖")
        print("2. 检查Chrome浏览器是否已安装")
        print("3. 检查网络连接是否正常")

if __name__ == "__main__":
    main()
