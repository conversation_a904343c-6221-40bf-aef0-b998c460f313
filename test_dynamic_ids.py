#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试动态ID - 专门测试动态生成的ID元素
"""

from DrissionPage import ChromiumPage, ChromiumOptions
import time
import re

def test_dynamic_ids():
    """测试动态ID元素"""
    print("🚀 测试动态ID元素")
    print("动态ID格式:")
    print("- 用户名: username_随机字符串 (如: username_Lu78x)")
    print("- 密码: password开头+随机字符串 (如: password3_Lu78x)")
    print("=" * 60)
    
    # 配置浏览器选项
    co = ChromiumOptions()
    co.headless(False)  # 可视模式
    co.set_argument('--disable-blink-features=AutomationControlled')
    co.set_argument('--disable-dev-shm-usage')
    co.set_argument('--no-sandbox')
    co.set_user_agent('Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHT<PERSON>, like Gecko) Chrome/120.0.0.0 Safari/537.36')
    
    try:
        # 创建页面对象
        page = ChromiumPage(co)
        
        # 访问发帖页面
        target_url = 'https://086wow.cn/forum.php?mod=post&action=newthread&fid=2'
        print(f"🔍 访问发帖页面: {target_url}")
        
        page.get(target_url, retry=3, interval=2, timeout=15)
        time.sleep(5)  # 等待页面完全加载
        
        print(f"📄 当前页面标题: {page.title}")
        print(f"📄 当前页面URL: {page.url}")
        print("-" * 50)
        
        # 查找所有输入框，分析ID模式
        print("🔍 分析页面中的所有输入框:")
        all_inputs = page.eles('xpath://input')
        
        username_inputs = []
        password_inputs = []
        other_inputs = []
        
        for i, input_elem in enumerate(all_inputs):
            input_id = input_elem.attr('id') or ''
            input_name = input_elem.attr('name') or ''
            input_type = input_elem.attr('type') or ''
            input_class = input_elem.attr('class') or ''
            
            print(f"  输入框 {i+1}:")
            print(f"    ID: {input_id}")
            print(f"    Name: {input_name}")
            print(f"    Type: {input_type}")
            print(f"    Class: {input_class}")
            
            # 分类输入框
            if 'username' in input_id.lower() or input_name == 'username':
                username_inputs.append(input_elem)
                print(f"    >>> 🎯 这是用户名输入框!")
            elif 'password' in input_id.lower() or input_name == 'password' or input_type == 'password':
                password_inputs.append(input_elem)
                print(f"    >>> 🎯 这是密码输入框!")
            else:
                other_inputs.append(input_elem)
            
            print()
        
        print("-" * 30)
        
        # 测试动态ID选择器
        print("🔍 测试动态ID选择器:")
        
        # 测试用户名输入框
        print("  用户名输入框测试:")
        username_selectors = [
            ('starts-with username_', 'xpath://input[starts-with(@id, "username_")]'),
            ('contains username', 'xpath://input[contains(@id, "username")]'),
            ('name=username', 'xpath://input[@name="username"]'),
            ('text+px class', 'xpath://input[@type="text" and contains(@class, "px")]')
        ]
        
        found_username = None
        for test_name, selector in username_selectors:
            element = page.ele(selector, timeout=2)
            if element:
                print(f"    ✅ {test_name}: 找到")
                print(f"       实际ID: {element.attr('id')}")
                if not found_username:
                    found_username = element
            else:
                print(f"    ❌ {test_name}: 未找到")
        
        print()
        
        # 测试密码输入框
        print("  密码输入框测试:")
        password_selectors = [
            ('starts-with password', 'xpath://input[starts-with(@id, "password")]'),
            ('contains password', 'xpath://input[contains(@id, "password")]'),
            ('name=password', 'xpath://input[@name="password"]'),
            ('type=password', 'xpath://input[@type="password"]'),
            ('password+px class', 'xpath://input[@type="password" and contains(@class, "px")]')
        ]
        
        found_password = None
        for test_name, selector in password_selectors:
            element = page.ele(selector, timeout=2)
            if element:
                print(f"    ✅ {test_name}: 找到")
                print(f"       实际ID: {element.attr('id')}")
                if not found_password:
                    found_password = element
            else:
                print(f"    ❌ {test_name}: 未找到")
        
        print("-" * 30)
        
        # 分析ID模式
        print("🔍 ID模式分析:")
        if found_username:
            username_id = found_username.attr('id')
            print(f"  用户名输入框ID: {username_id}")
            if username_id:
                # 提取模式
                match = re.match(r'username_(.+)', username_id)
                if match:
                    suffix = match.group(1)
                    print(f"  用户名ID后缀: {suffix}")
                    
                    # 预测密码框ID
                    predicted_password_ids = [
                        f"password_{suffix}",
                        f"password3_{suffix}",
                        f"pwd_{suffix}"
                    ]
                    
                    print(f"  预测的密码框ID可能是:")
                    for pred_id in predicted_password_ids:
                        print(f"    - {pred_id}")
                        test_elem = page.ele(f'xpath://input[@id="{pred_id}"]', timeout=1)
                        if test_elem:
                            print(f"      ✅ 找到了!")
        
        if found_password:
            password_id = found_password.attr('id')
            print(f"  密码输入框ID: {password_id}")
        
        print("-" * 50)
        
        # 测试实际交互
        if found_username and found_password:
            print("🎉 找到了用户名和密码输入框!")
            print("🔍 测试元素交互:")
            
            try:
                # 测试输入
                print("  测试用户名输入...")
                found_username.clear()
                found_username.input("test_user")
                print("  ✅ 用户名输入成功")
                
                print("  测试密码输入...")
                found_password.clear()
                found_password.input("test_pass")
                print("  ✅ 密码输入成功")
                
                print("  ✅ 动态ID元素交互测试成功!")
                
            except Exception as e:
                print(f"  ❌ 元素交互失败: {e}")
        else:
            print("❌ 未能找到完整的登录表单")
        
        print("\n" + "=" * 50)
        print("测试完成！请查看浏览器窗口确认页面状态。")
        print("按回车键关闭浏览器...")
        input()
        
        page.quit()
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        try:
            page.quit()
        except:
            pass
        return False

def main():
    """主函数"""
    print("🔍 动态ID测试工具")
    print("此工具专门测试动态生成的ID元素")
    print("=" * 50)
    
    # 检查依赖
    try:
        from DrissionPage import ChromiumPage, ChromiumOptions
        print("✅ DrissionPage 导入成功")
    except ImportError as e:
        print(f"❌ DrissionPage 导入失败: {e}")
        print("请先安装: pip install DrissionPage")
        return
    
    print("\n开始测试...")
    test_dynamic_ids()

if __name__ == "__main__":
    main()
