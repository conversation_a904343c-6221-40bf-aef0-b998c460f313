#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试滑块验证码 - 专门测试滑块验证码的处理
"""

from DrissionPage import ChromiumPage, ChromiumOptions
import time
import random

def get_slide_tracks(distance):
    """生成滑动轨迹"""
    tracks = []
    current = 0
    mid = distance * 4 / 5
    t = 0.2
    v = 0
    
    while current < distance:
        if current < mid:
            a = 2
        else:
            a = -3
        
        v0 = v
        v = v0 + a * t
        s = v0 * t + 0.5 * a * t * t
        current += s
        tracks.append(round(s))
    
    return tracks

def test_slider_captcha():
    """测试滑块验证码"""
    print("🚀 测试滑块验证码")
    print("此工具专门测试滑块验证码的识别和处理")
    print("=" * 60)
    
    # 配置浏览器选项
    co = ChromiumOptions()
    co.headless(False)  # 可视模式
    co.set_argument('--disable-blink-features=AutomationControlled')
    co.set_argument('--disable-dev-shm-usage')
    co.set_argument('--no-sandbox')
    co.set_user_agent('Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36')
    
    try:
        # 创建页面对象
        page = ChromiumPage(co)
        
        # 访问发帖页面
        target_url = 'https://086wow.cn/forum.php?mod=post&action=newthread&fid=2'
        print(f"🔍 访问发帖页面: {target_url}")
        
        page.get(target_url, retry=3, interval=2, timeout=15)
        time.sleep(5)
        
        print(f"📄 当前页面标题: {page.title}")
        print("-" * 50)
        
        # 先填写登录信息
        print("🔍 查找并填写登录信息:")
        username_input = page.ele('xpath://input[starts-with(@id, "username_")]', timeout=5)
        password_input = page.ele('xpath://input[@type="password"]', timeout=5)
        
        if username_input and password_input:
            print("✅ 找到登录输入框")
            username_input.input("test_user")
            password_input.input("test_pass")
            print("✅ 填写测试登录信息")
        else:
            print("❌ 未找到登录输入框")
            return False
        
        # 点击登录按钮触发验证码
        login_btn = page.ele('xpath://button[@type="submit" and contains(@class, "pn")]', timeout=5)
        if login_btn:
            print("🔘 点击登录按钮触发验证码...")
            login_btn.click()
            time.sleep(3)
        else:
            print("❌ 未找到登录按钮")
            return False
        
        print("-" * 30)
        
        # 检查是否出现滑块验证码
        print("🔍 检查滑块验证码:")
        
        # 检查页面内容
        if '滑块' in page.html or 'slider' in page.html or 'captcha' in page.html:
            print("✅ 检测到滑块验证码相关内容")
        else:
            print("❌ 未检测到滑块验证码")
            print("页面内容片段:")
            print(page.html[:1000])
            return False
        
        # 查找滑块元素
        print("🔍 查找滑块元素:")
        slider_selectors = [
            ('class包含slider', 'xpath://div[contains(@class, "slider")]'),
            ('class包含slide', 'xpath://div[contains(@class, "slide")]'),
            ('id包含slider', 'xpath://div[contains(@id, "slider")]'),
            ('id包含slide', 'xpath://div[contains(@id, "slide")]'),
            ('captcha相关', 'xpath://*[contains(@class, "captcha")]//div'),
            ('包含滑动文本', 'xpath://*[contains(text(), "滑动")]'),
            ('role=slider', 'xpath://div[@role="slider"]'),
            ('所有可拖拽元素', 'xpath://*[@draggable="true"]'),
            ('所有div元素', 'xpath://div')
        ]
        
        slider_element = None
        for test_name, selector in slider_selectors:
            elements = page.eles(selector, timeout=2)
            if elements:
                print(f"  ✅ {test_name}: 找到 {len(elements)} 个元素")
                for i, elem in enumerate(elements[:3]):  # 只显示前3个
                    print(f"     元素{i+1}: class='{elem.attr('class')}', id='{elem.attr('id')}'")
                    if not slider_element and ('slider' in str(elem.attr('class')).lower() or 
                                             'slide' in str(elem.attr('class')).lower()):
                        slider_element = elem
                        print(f"     >>> 🎯 选择这个作为滑块元素!")
            else:
                print(f"  ❌ {test_name}: 未找到")
        
        if not slider_element:
            print("❌ 未找到合适的滑块元素")
            return False
        
        print("-" * 30)
        
        # 测试滑动
        print("🔍 测试滑块滑动:")
        slide_distances = [100, 150, 200, 120, 180]
        
        for distance in slide_distances:
            try:
                print(f"  测试滑动距离: {distance}px")
                
                # 生成滑动轨迹
                tracks = get_slide_tracks(distance)
                print(f"  生成轨迹: {len(tracks)}步")
                
                # 执行滑动
                print("  开始滑动...")
                page.actions.hold(slider_element)
                for track in tracks:
                    page.actions.move(offset_x=track, offset_y=round(random.uniform(-2.0, 2.0), 1), duration=0.1)
                time.sleep(0.1)
                page.actions.release()
                
                print("  ✅ 滑动完成，等待验证结果...")
                time.sleep(3)
                
                # 检查验证结果
                current_html = page.html
                if '验证成功' in current_html:
                    print(f"  🎉 验证成功！距离: {distance}px")
                    break
                elif '验证失败' in current_html or '重试' in current_html:
                    print(f"  ❌ 验证失败，尝试下一个距离...")
                    continue
                elif '发表新主题' in current_html or '登录成功' in current_html:
                    print(f"  🎉 登录成功！距离: {distance}px")
                    break
                else:
                    print(f"  ⚠️ 验证结果不明确，假设成功")
                    break
                    
            except Exception as e:
                print(f"  ❌ 滑动失败: {e}")
                continue
        
        print("\n" + "=" * 50)
        print("测试完成！请查看浏览器窗口确认验证结果。")
        print("按回车键关闭浏览器...")
        input()
        
        page.quit()
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        try:
            page.quit()
        except:
            pass
        return False

def main():
    """主函数"""
    print("🔍 滑块验证码测试工具")
    print("此工具专门测试滑块验证码的处理")
    print("=" * 50)
    
    # 检查依赖
    try:
        from DrissionPage import ChromiumPage, ChromiumOptions
        print("✅ DrissionPage 导入成功")
    except ImportError as e:
        print(f"❌ DrissionPage 导入失败: {e}")
        print("请先安装: pip install DrissionPage")
        return
    
    print("\n开始测试...")
    test_slider_captcha()

if __name__ == "__main__":
    main()
