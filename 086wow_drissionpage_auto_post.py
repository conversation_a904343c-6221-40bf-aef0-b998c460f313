from DrissionPage import ChromiumPage, ChromiumOptions
import re
import time
import random
import json

# 账号列表 (账号, 密码)
accounts = [
    ('WiWow01', '123456wb'),
]

# 目标URL和发帖参数
TARGET_URL = 'https://086wow.cn/forum.php?mod=post&action=newthread&fid=2'
LOGIN_URL = 'https://086wow.cn/forum.php?mod=post&action=newthread&fid=2'  # 直接访问发帖页面，会自动跳转到登录
FID = '2'  # 版块ID

# 发帖内容
POST_TITLE = '乌龟西格玛 —— 60级1.17乌龟魔爽单刷服'
POST_MESSAGE = """
乌龟西格玛 —— 1.17乌龟魔爽单刷首发【免费体验服】

特色玩法抢先看

加入我们，一起开荒！
点击链接加入QQ群：
http://qm.qq.com/cgi-bin/qm/qr?_wv=1027&k=xxx&group_code=*********

QQ群号：*********
QQ群号：*********
QQ群号：*********

参与宣传，领取奖励！
额
"""

def solve_question(question):
    """简单四则运算自动答题"""
    # 匹配 "数字 运算符 数字 = ?" 格式，支持 +、-、*、/
    m = re.search(r"(\d+)\s*([\+\-\*\/×÷])\s*(\d+)\s*=\s*\?", question)
    if not m:
        return ""
    a, op, b = int(m.group(1)), m.group(2), int(m.group(3))
    
    if op == '+':
        return str(a + b)
    elif op == '-':
        return str(a - b)
    elif op in ['*', '×']:
        return str(a * b)
    elif op in ['/', '÷']:
        return str(a // b)  # 整数除法
    else:
        return ""

def get_slide_tracks(distance):
    """生成滑块运动轨迹"""
    value = round(random.uniform(0.55, 0.78), 2)
    v, t, sum1 = 0, 0.3, 0
    plus = []
    mid = distance * value
    while sum1 < distance:
        if sum1 < mid:
            a = round(random.uniform(2.5, 3.5), 1)
        else:
            a = -round(random.uniform(2.0, 3.0), 1)
        s = v * t + 0.5 * a * (t ** 2)
        v = v + a * t
        sum1 += s
        plus.append(round(s))
    return plus

def handle_slider_captcha(page, username):
    """处理滑块验证码 - 简化版本，不依赖图片识别"""
    try:
        print(f"[{username}] 🔍 检测到滑块验证码，开始处理...")

        # 等待滑块元素加载，尝试多种可能的选择器
        slider_selectors = [
            'xpath://div[contains(@class, "slider")]',
            'xpath://div[contains(@class, "slide")]',
            'xpath://div[contains(@id, "slider")]',
            'xpath://div[contains(@id, "slide")]',
            'xpath://*[contains(@class, "captcha")]//div',
            'xpath://*[contains(text(), "滑动")]',
            'xpath://div[@role="slider"]'
        ]

        slider_element = None
        for selector in slider_selectors:
            slider_element = page.ele(selector, timeout=3)
            if slider_element:
                print(f"[{username}] ✅ 找到滑块元素: {selector}")
                break

        if not slider_element:
            print(f"[{username}] ❌ 未找到滑块元素")
            return False

        # 尝试简单的滑动策略
        print(f"[{username}] 🎯 开始滑动验证...")

        # 策略1: 尝试不同的滑动距离
        slide_distances = [100, 150, 200, 120, 180]

        for distance in slide_distances:
            try:
                print(f"[{username}] 📍 尝试滑动距离: {distance}px")

                # 生成滑动轨迹
                tracks = get_slide_tracks(distance)

                # 执行滑动
                page.actions.hold(slider_element)
                for track in tracks:
                    page.actions.move(offset_x=track, offset_y=round(random.uniform(-2.0, 2.0), 1), duration=0.1)
                time.sleep(0.1)
                page.actions.release()

                # 等待验证结果
                time.sleep(2)

                # 检查是否验证成功（页面是否有变化）
                if '验证成功' in page.html or '登录成功' in page.html or '发表新主题' in page.html:
                    print(f"[{username}] ✅ 滑块验证成功！")
                    return True
                elif '验证失败' in page.html or '重试' in page.html:
                    print(f"[{username}] ⚠️ 滑动距离 {distance}px 验证失败，尝试下一个...")
                    continue
                else:
                    # 如果没有明确的成功/失败提示，假设成功
                    print(f"[{username}] ✅ 滑块验证完成（距离: {distance}px）")
                    return True

            except Exception as e:
                print(f"[{username}] ⚠️ 滑动距离 {distance}px 执行失败: {e}")
                continue

        print(f"[{username}] ❌ 所有滑动距离都尝试失败")
        return False

    except Exception as e:
        print(f"[{username}] ❌ 处理滑块验证码失败: {e}")
        return False

def login_with_drissionpage(username, password):
    """使用DrissionPage登录"""
    print(f"[{username}] 🚀 开始DrissionPage登录流程...")

    # 配置浏览器选项
    co = ChromiumOptions()
    co.headless(False)  # 设置为True可以无头模式运行
    co.set_argument('--disable-blink-features=AutomationControlled')
    co.set_argument('--disable-dev-shm-usage')
    co.set_argument('--no-sandbox')
    co.set_user_agent('Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36')

    try:
        # 创建页面对象
        page = ChromiumPage(co)

        # 直接访问发帖页面，会自动跳转到登录页面
        print(f"[{username}] 📄 访问发帖页面（会自动跳转到登录）...")
        page.get(LOGIN_URL, retry=3, interval=2, timeout=15)
        time.sleep(5)  # 增加等待时间，确保页面完全加载

        print(f"[{username}] 📄 当前页面标题: {page.title}")
        print(f"[{username}] 📄 当前页面URL: {page.url}")
        
        # 填写用户名和密码 - 支持多种登录表单
        username_input = None
        password_input = None

        # 尝试找到用户名输入框（支持动态ID）
        username_selectors = [
            'xpath://input[@name="username"]',
            'xpath://input[starts-with(@id, "username_")]',  # 动态ID：username_随机字符串
            'xpath://input[contains(@id, "username")]',
            'xpath://input[@type="text" and contains(@class, "px")]'
        ]

        for selector in username_selectors:
            username_input = page.ele(selector, timeout=3)
            if username_input:
                break

        # 尝试找到密码输入框（支持动态ID）
        password_selectors = [
            'xpath://input[@name="password"]',
            'xpath://input[starts-with(@id, "password")]',  # 动态ID：password开头
            'xpath://input[@type="password"]',
            'xpath://input[@type="password" and contains(@class, "px")]'
        ]

        for selector in password_selectors:
            password_input = page.ele(selector, timeout=3)
            if password_input:
                break

        if not username_input or not password_input:
            print(f"[{username}] ❌ 未找到用户名或密码输入框")
            print(f"[{username}] 用户名输入框: {'✅' if username_input else '❌'}")
            print(f"[{username}] 密码输入框: {'✅' if password_input else '❌'}")
            page.quit()
            return None

        print(f"[{username}] ✏️ 填写登录信息...")
        username_input.clear()
        username_input.input(username, clear=True)
        time.sleep(0.3)  # 减少等待时间

        password_input.clear()
        password_input.input(password, clear=True)
        time.sleep(0.3)  # 减少等待时间
        
        # 处理数学验证码
        math_question = page.ele('xpath://div[contains(@class, "secqaa")]', timeout=5)
        if not math_question:
            # 尝试其他可能的验证码元素
            math_question = page.ele('xpath://*[contains(text(), "=")]', timeout=3)

        if math_question:
            question_text = math_question.text
            answer = solve_question(question_text)
            if answer:
                # 尝试多种可能的答案输入框
                answer_selectors = [
                    'xpath://input[contains(@name, "secanswer")]',
                    'xpath://input[contains(@id, "secanswer")]',
                    'xpath://input[@type="text" and contains(@class, "px")]'
                ]

                answer_input = None
                for selector in answer_selectors:
                    answer_input = page.ele(selector, timeout=2)
                    if answer_input:
                        break

                if answer_input:
                    print(f"[{username}] 🔍 数学验证码: {question_text}, 答案: {answer}")
                    answer_input.input(answer, clear=True)
                    time.sleep(0.3)  # 减少等待时间
                else:
                    print(f"[{username}] ⚠️ 找到验证码问题但未找到答案输入框")
        
        # 点击登录按钮 - 支持多种登录按钮
        login_btn = None
        login_selectors = [
            'xpath://button[@type="submit" and contains(@class, "pn")]',  # 新的按钮样式
            'xpath://button[@type="submit" and contains(@class, "vm")]',  # 包含vm class
            'xpath://button[contains(@class, "pn vm")]',  # 你提供的具体class
            'xpath://button[@type="submit" and contains(., "登录")]',  # 包含登录文本
            'xpath://button[@name="loginsubmit"]',
            'xpath://input[@name="loginsubmit"]',
            'xpath://button[contains(@class, "pn") and contains(text(), "登录")]',
            'xpath://button[@value="true" and @name="loginsubmit"]'
        ]

        for selector in login_selectors:
            login_btn = page.ele(selector, timeout=3)
            if login_btn:
                break

        if login_btn:
            print(f"[{username}] 🔘 点击登录按钮...")
            try:
                # 尝试多种点击方式
                login_btn.click()
                time.sleep(2)
            except Exception as e:
                print(f"[{username}] ⚠️ 普通点击失败，尝试其他方式: {e}")
                try:
                    # 尝试JavaScript点击
                    page.run_js("arguments[0].click();", login_btn)
                    time.sleep(2)
                    print(f"[{username}] ✅ JavaScript点击成功")
                except Exception as e2:
                    print(f"[{username}] ❌ JavaScript点击也失败: {e2}")
                    # 尝试提交表单
                    try:
                        form = page.ele('xpath://form', timeout=5)
                        if form:
                            form.submit()
                            print(f"[{username}] ✅ 表单提交成功")
                            time.sleep(2)
                        else:
                            print(f"[{username}] ❌ 未找到表单")
                            page.quit()
                            return None
                    except Exception as e3:
                        print(f"[{username}] ❌ 表单提交失败: {e3}")
                        page.quit()
                        return None
        else:
            print(f"[{username}] ❌ 未找到登录按钮")
            page.quit()
            return None
        
        # 检查是否需要处理滑块验证码
        if '滑块' in page.html or 'slider' in page.html or 'captcha' in page.html:
            if not handle_slider_captcha(page, username):
                page.quit()
                return None
        
        # 检查登录结果 - 登录成功会跳转到发帖页面
        time.sleep(3)
        if ('发表新主题' in page.html or '发帖' in page.html or 'newthread' in page.html or
            '欢迎您回来' in page.html or '退出' in page.html or username in page.html):
            print(f"[{username}] ✅ 登录成功！已自动跳转到发帖页面")
            return page
        else:
            print(f"[{username}] ❌ 登录失败")
            print(f"[{username}] 页面内容片段: {page.html[:500]}")
            page.quit()
            return None
            
    except Exception as e:
        print(f"[{username}] ❌ 登录过程中出现异常: {e}")
        try:
            page.quit()
        except:
            pass
        return None

def check_post_page(page, username):
    """检查是否在发帖页面"""
    try:
        print(f"[{username}] 🎯 检查发帖页面状态...")

        # 如果不在发帖页面，尝试访问
        if not ('发表新主题' in page.html or '发帖' in page.html or 'newthread' in page.html):
            print(f"[{username}] 📄 当前不在发帖页面，尝试访问...")
            page.get(TARGET_URL, retry=3, interval=2, timeout=15)
            time.sleep(3)

        if '发表新主题' in page.html or '发帖' in page.html or 'newthread' in page.html:
            print(f"[{username}] ✅ 已在发帖页面！")
            return True
        elif '尚未登录' in page.html or '没有权限' in page.html or 'login' in page.html:
            print(f"[{username}] ❌ 需要重新登录")
            return False
        else:
            print(f"[{username}] ⚠️ 页面状态未知")
            return False

    except Exception as e:
        print(f"[{username}] ❌ 检查发帖页面失败: {e}")
        return False

def post_thread_drissionpage(page, username):
    """使用DrissionPage发帖"""
    print(f"[{username}] 📝 开始发帖流程...")
    
    try:
        # 填写标题
        title_input = page.ele('xpath://input[@name="subject"]', timeout=10)
        if title_input:
            print(f"[{username}] ✏️ 填写标题...")
            title_input.clear()
            title_input.input(POST_TITLE)
            time.sleep(1)
        
        # 填写内容 - 尝试多种可能的编辑器
        content_filled = False
        
        # 尝试富文本编辑器
        editor_frame = page.ele('xpath://iframe[contains(@id, "editor")]', timeout=5)
        if editor_frame:
            page.to_frame(editor_frame)
            editor_body = page.ele('xpath://body', timeout=5)
            if editor_body:
                print(f"[{username}] ✏️ 在富文本编辑器中填写内容...")
                editor_body.input(POST_MESSAGE)
                content_filled = True
            page.to_main_frame()
        
        # 如果富文本编辑器不可用，尝试普通文本框
        if not content_filled:
            message_textarea = page.ele('xpath://textarea[@name="message"]', timeout=5)
            if message_textarea:
                print(f"[{username}] ✏️ 在文本框中填写内容...")
                message_textarea.clear()
                message_textarea.input(POST_MESSAGE)
                content_filled = True
        
        if not content_filled:
            print(f"[{username}] ❌ 未找到内容输入框")
            return False
        
        time.sleep(2)
        
        # 处理发帖验证码
        math_question = page.ele('xpath://div[contains(@class, "secqaa")]', timeout=5)
        if math_question:
            question_text = math_question.text
            answer = solve_question(question_text)
            if answer:
                answer_input = page.ele('xpath://input[contains(@name, "secanswer")]', timeout=5)
                if answer_input:
                    print(f"[{username}] 🔍 发帖验证码: {question_text}, 答案: {answer}")
                    answer_input.input(answer)
                    time.sleep(1)
        
        # 点击发帖按钮
        submit_btn = page.ele('xpath://button[@name="topicsubmit"] | xpath://input[@name="topicsubmit"]', timeout=10)
        if submit_btn:
            print(f"[{username}] 🔘 点击发帖按钮...")
            submit_btn.click()
            time.sleep(5)
        
        # 检查发帖结果
        if any(indicator in page.html for indicator in ['发布成功', '主题已发布', '现在将转入', 'tid=']):
            print(f"[{username}] ✅ 发帖成功！")
            return True
        else:
            print(f"[{username}] ❌ 发帖失败")
            print(f"[{username}] 页面内容片段: {page.html[:500]}")
            return False
            
    except Exception as e:
        print(f"[{username}] ❌ 发帖过程中出现异常: {e}")
        return False

def login_and_post_drissionpage(username, password):
    """单个账号的DrissionPage登录和发帖流程"""
    print(f"\n➡️ 开始处理账号 {username}...")
    
    # 登录
    page = login_with_drissionpage(username, password)
    if not page:
        return False
    
    try:
        time.sleep(2)

        # 检查发帖页面
        if not check_post_page(page, username):
            page.quit()
            return False
        
        # 执行发帖
        post_success = post_thread_drissionpage(page, username)
        
        if post_success:
            print(f"[{username}] 🎉 发帖成功！")
            page.quit()
            return True
        else:
            print(f"[{username}] ❌ 发帖失败")
            page.quit()
            return False
            
    except Exception as e:
        print(f"[{username}] ❌ 处理过程中出现异常: {e}")
        try:
            page.quit()
        except:
            pass
        return False

def main():
    """主函数"""
    print("🚀 开始 086wow.cn DrissionPage自动发帖程序")
    print(f"📋 账号数量: {len(accounts)}")
    print(f"🎯 目标页面: {TARGET_URL}")
    print(f"📝 发帖标题: {POST_TITLE}")
    print("-" * 50)
    
    # ddddocr现在只用于数学验证码，滑块验证码已简化
    print("ℹ️ 滑块验证码已简化，不再依赖图片识别")
    
    # 询问用户操作模式
    while True:
        print("\n请选择操作模式:")
        print("1. 立即执行一轮发帖")
        print("2. 循环发帖模式（每21分钟一轮）")
        print("3. 退出程序")
        
        user_choice = input("请输入选择 (1/2/3): ").strip()
        
        if user_choice == '1':
            # 单轮发帖
            print(f"\n🚩 开始单轮发帖 —— 当前时间：{time.strftime('%Y-%m-%d %H:%M:%S')}")
            success_count = 0
            
            for username, password in accounts:
                result = login_and_post_drissionpage(username, password)
                if result:
                    success_count += 1
                time.sleep(5)  # 账号间隔5秒
            
            print(f"\n✅ 单轮发帖完成！成功: {success_count}/{len(accounts)}")
            
        elif user_choice == '2':
            # 循环发帖模式
            print(f"\n🔄 进入循环发帖模式...")
            round_count = 0
            
            try:
                while True:
                    round_count += 1
                    print(f"\n🚩 第 {round_count} 轮发帖开始 —— 当前时间：{time.strftime('%Y-%m-%d %H:%M:%S')}")
                    success_count = 0
                    
                    for username, password in accounts:
                        result = login_and_post_drissionpage(username, password)
                        if result:
                            success_count += 1
                        time.sleep(5)  # 账号间隔5秒
                    
                    print(f"\n✅ 第 {round_count} 轮发帖完毕！成功: {success_count}/{len(accounts)}")
                    print(f"💤 休息 21 分钟后开始下一轮...")
                    time.sleep(21 * 60)  # 休息21分钟
                    
            except KeyboardInterrupt:
                print(f"\n\n⏹️ 用户中断程序，已完成 {round_count} 轮发帖")
                break
                
        elif user_choice == '3':
            print(f"\n👋 程序退出")
            break
            
        else:
            print(f"\n❌ 无效选择，请输入 1、2 或 3")

if __name__ == "__main__":
    main()
