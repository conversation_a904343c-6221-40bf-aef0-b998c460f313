#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
安装DrissionPage自动发帖程序所需的依赖包
"""

import subprocess
import sys
import os

def install_package(package_name):
    """安装单个包"""
    try:
        print(f"正在安装 {package_name}...")
        result = subprocess.run([sys.executable, "-m", "pip", "install", package_name], 
                              capture_output=True, text=True, check=True)
        print(f"✅ {package_name} 安装成功")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ {package_name} 安装失败: {e}")
        print(f"错误输出: {e.stderr}")
        return False

def check_package(package_name):
    """检查包是否已安装"""
    try:
        __import__(package_name)
        print(f"✅ {package_name} 已安装")
        return True
    except ImportError:
        print(f"❌ {package_name} 未安装")
        return False

def main():
    """主函数"""
    print("🚀 开始安装DrissionPage自动发帖程序依赖包")
    print("=" * 50)
    
    # 需要安装的包列表
    packages = [
        "DrissionPage",
        # "ddddocr",  # 不再需要，滑块验证码已简化
        "Pillow",
        "opencv-python"
    ]
    
    # 检查Python版本
    python_version = sys.version_info
    print(f"Python版本: {python_version.major}.{python_version.minor}.{python_version.micro}")
    
    if python_version.major < 3 or (python_version.major == 3 and python_version.minor < 6):
        print("❌ Python版本过低，需要Python 3.6或更高版本")
        return
    
    print("✅ Python版本检查通过")
    print("-" * 50)
    
    # 升级pip
    print("正在升级pip...")
    try:
        subprocess.run([sys.executable, "-m", "pip", "install", "--upgrade", "pip"], 
                      capture_output=True, text=True, check=True)
        print("✅ pip升级成功")
    except subprocess.CalledProcessError:
        print("⚠️ pip升级失败，继续安装其他包")
    
    print("-" * 50)
    
    # 安装包
    success_count = 0
    for package in packages:
        if install_package(package):
            success_count += 1
        print()
    
    print("=" * 50)
    print(f"安装完成！成功: {success_count}/{len(packages)}")
    
    # 验证安装
    print("\n验证安装结果:")
    print("-" * 30)
    
    # 检查DrissionPage
    try:
        from DrissionPage import ChromiumPage, ChromiumOptions
        print("✅ DrissionPage 导入成功")
    except ImportError as e:
        print(f"❌ DrissionPage 导入失败: {e}")
    
    # 检查ddddocr
    try:
        import ddddocr
        print("✅ ddddocr 导入成功")
    except ImportError as e:
        print(f"❌ ddddocr 导入失败: {e}")
    
    # 检查PIL
    try:
        from PIL import Image
        print("✅ Pillow 导入成功")
    except ImportError as e:
        print(f"❌ Pillow 导入失败: {e}")
    
    # 检查cv2
    try:
        import cv2
        print("✅ opencv-python 导入成功")
    except ImportError as e:
        print(f"❌ opencv-python 导入失败: {e}")
    
    print("\n" + "=" * 50)
    print("安装完成！")
    print("\n使用说明:")
    print("1. 运行 python 086wow_drissionpage_auto_post.py 开始自动发帖")
    print("2. 首次运行会自动下载Chrome浏览器驱动")
    print("3. 如果遇到问题，请检查Chrome浏览器是否已安装")
    print("\n注意事项:")
    print("- 确保Chrome浏览器已安装并且版本较新")
    print("- 网络连接正常")
    print("- 账号密码正确")

if __name__ == "__main__":
    main()
