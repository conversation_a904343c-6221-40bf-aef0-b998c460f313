#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试登录流程 - 验证直接访问发帖页面的登录流程
"""

from DrissionPage import ChromiumPage, ChromiumOptions
import time

def test_login_flow():
    """测试登录流程"""
    print("🚀 测试086wow.cn登录流程")
    print("=" * 50)
    
    # 配置浏览器选项
    co = ChromiumOptions()
    co.headless(False)  # 可视模式，方便观察
    co.set_argument('--disable-blink-features=AutomationControlled')
    co.set_argument('--disable-dev-shm-usage')
    co.set_argument('--no-sandbox')
    co.set_user_agent('Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36')
    
    try:
        # 创建页面对象
        page = ChromiumPage(co)
        
        # 直接访问发帖页面
        target_url = 'https://086wow.cn/forum.php?mod=post&action=newthread&fid=2'
        print(f"🔍 访问发帖页面: {target_url}")
        print("（如果未登录会自动跳转到登录页面）")
        
        page.get(target_url, retry=3, interval=2, timeout=15)
        time.sleep(3)
        
        print(f"📄 当前页面标题: {page.title}")
        print(f"📄 当前页面URL: {page.url}")
        
        # 检查页面状态
        if '登录' in page.html or 'login' in page.html:
            print("✅ 成功跳转到登录页面")
            
            # 查找登录表单元素 - 支持多种可能的元素
            username_input = None
            password_input = None
            login_btn = None

            # 尝试找到用户名输入框（支持动态ID）
            username_selectors = [
                'xpath://input[@name="username"]',
                'xpath://input[starts-with(@id, "username_")]',  # 动态ID
                'xpath://input[contains(@id, "username")]',
                'xpath://input[@type="text" and contains(@class, "px")]'
            ]
            for selector in username_selectors:
                username_input = page.ele(selector, timeout=2)
                if username_input:
                    break

            # 尝试找到密码输入框（支持动态ID）
            password_selectors = [
                'xpath://input[@name="password"]',
                'xpath://input[starts-with(@id, "password")]',  # 动态ID
                'xpath://input[@type="password"]',
                'xpath://input[@type="password" and contains(@class, "px")]'
            ]
            for selector in password_selectors:
                password_input = page.ele(selector, timeout=2)
                if password_input:
                    break

            # 尝试找到登录按钮
            login_selectors = [
                'xpath://button[@type="submit" and contains(@class, "pn")]',  # 新的按钮样式
                'xpath://button[@type="submit" and contains(@class, "vm")]',  # 包含vm class
                'xpath://button[contains(@class, "pn vm")]',  # 具体class组合
                'xpath://button[@type="submit" and contains(., "登录")]',  # 包含登录文本
                'xpath://button[@name="loginsubmit"]',
                'xpath://input[@name="loginsubmit"]',
                'xpath://button[contains(@class, "pn") and contains(text(), "登录")]',
                'xpath://button[@value="true" and @name="loginsubmit"]'
            ]
            for selector in login_selectors:
                login_btn = page.ele(selector, timeout=2)
                if login_btn:
                    break
            
            if username_input and password_input and login_btn:
                print("✅ 找到登录表单元素")
                print("  - 用户名输入框: ✅")
                print("  - 密码输入框: ✅") 
                print("  - 登录按钮: ✅")
            else:
                print("❌ 登录表单元素不完整")
                print(f"  - 用户名输入框: {'✅' if username_input else '❌'}")
                print(f"  - 密码输入框: {'✅' if password_input else '❌'}")
                print(f"  - 登录按钮: {'✅' if login_btn else '❌'}")
            
            # 检查验证码
            math_question = page.ele('xpath://div[contains(@class, "secqaa")]', timeout=5)
            if math_question:
                print("🔍 发现数学验证码")
                print(f"  问题: {math_question.text}")
            else:
                print("ℹ️ 未发现数学验证码")
            
            # 检查滑块验证码
            if '滑块' in page.html or 'slider' in page.html or 'captcha' in page.html:
                print("🔍 可能存在滑块验证码")
            else:
                print("ℹ️ 未发现滑块验证码")
                
        elif '发表新主题' in page.html or '发帖' in page.html or 'newthread' in page.html:
            print("✅ 已经在发帖页面（可能已登录）")
            
            # 查找发帖表单元素
            title_input = page.ele('xpath://input[@name="subject"]', timeout=5)
            message_textarea = page.ele('xpath://textarea[@name="message"]', timeout=5)
            editor_frame = page.ele('xpath://iframe[contains(@id, "editor")]', timeout=5)
            submit_btn = page.ele('xpath://button[@name="topicsubmit"] | xpath://input[@name="topicsubmit"]', timeout=5)
            
            print("📝 发帖表单元素检查:")
            print(f"  - 标题输入框: {'✅' if title_input else '❌'}")
            print(f"  - 内容文本框: {'✅' if message_textarea else '❌'}")
            print(f"  - 富文本编辑器: {'✅' if editor_frame else '❌'}")
            print(f"  - 发帖按钮: {'✅' if submit_btn else '❌'}")
            
        else:
            print("⚠️ 页面状态未知")
            print("页面内容片段:")
            print(page.html[:500])
        
        print("\n" + "=" * 50)
        print("测试完成！请查看浏览器窗口确认页面状态。")
        print("按回车键关闭浏览器...")
        input()
        
        page.quit()
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        try:
            page.quit()
        except:
            pass
        return False

def main():
    """主函数"""
    print("🔍 086wow.cn登录流程测试工具")
    print("此工具用于验证直接访问发帖页面的登录流程")
    print("=" * 50)
    
    # 检查依赖
    try:
        from DrissionPage import ChromiumPage, ChromiumOptions
        print("✅ DrissionPage 导入成功")
    except ImportError as e:
        print(f"❌ DrissionPage 导入失败: {e}")
        print("请先安装: pip install DrissionPage")
        return
    
    print("\n开始测试...")
    test_login_flow()

if __name__ == "__main__":
    main()
