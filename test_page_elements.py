#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试页面元素 - 详细检查登录表单元素
"""

from DrissionPage import ChromiumPage, ChromiumOptions
import time

def test_page_elements():
    """测试页面元素"""
    print("🚀 测试086wow.cn页面元素")
    print("=" * 50)
    
    # 配置浏览器选项
    co = ChromiumOptions()
    co.headless(False)  # 可视模式
    co.set_argument('--disable-blink-features=AutomationControlled')
    co.set_argument('--disable-dev-shm-usage')
    co.set_argument('--no-sandbox')
    co.set_user_agent('Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36')
    
    try:
        # 创建页面对象
        page = ChromiumPage(co)
        
        # 访问发帖页面
        target_url = 'https://086wow.cn/forum.php?mod=post&action=newthread&fid=2'
        print(f"🔍 访问发帖页面: {target_url}")
        
        page.get(target_url, retry=3, interval=2, timeout=15)
        time.sleep(5)  # 等待页面完全加载
        
        print(f"📄 当前页面标题: {page.title}")
        print(f"📄 当前页面URL: {page.url}")
        print("-" * 50)
        
        # 检查所有可能的用户名输入框
        print("🔍 查找用户名输入框:")
        username_selectors = [
            ('通用用户名', 'xpath://input[@name="username"]'),
            ('特定ID用户名', 'xpath://input[@id="username_Lu78x"]'),
            ('包含username的ID', 'xpath://input[contains(@id, "username")]'),
            ('所有文本输入框', 'xpath://input[@type="text"]')
        ]
        
        username_found = False
        for name, selector in username_selectors:
            element = page.ele(selector, timeout=2)
            if element:
                print(f"  ✅ {name}: 找到")
                print(f"     ID: {element.attr('id')}")
                print(f"     Name: {element.attr('name')}")
                print(f"     Class: {element.attr('class')}")
                username_found = True
            else:
                print(f"  ❌ {name}: 未找到")
        
        print("-" * 30)
        
        # 检查所有可能的密码输入框
        print("🔍 查找密码输入框:")
        password_selectors = [
            ('通用密码', 'xpath://input[@name="password"]'),
            ('特定ID密码', 'xpath://input[@id="password3_Lu78x"]'),
            ('包含password的ID', 'xpath://input[contains(@id, "password")]'),
            ('所有密码输入框', 'xpath://input[@type="password"]')
        ]
        
        password_found = False
        for name, selector in password_selectors:
            element = page.ele(selector, timeout=2)
            if element:
                print(f"  ✅ {name}: 找到")
                print(f"     ID: {element.attr('id')}")
                print(f"     Name: {element.attr('name')}")
                print(f"     Class: {element.attr('class')}")
                password_found = True
            else:
                print(f"  ❌ {name}: 未找到")
        
        print("-" * 30)
        
        # 检查所有可能的登录按钮
        print("🔍 查找登录按钮:")
        login_selectors = [
            ('通用登录按钮', 'xpath://button[@name="loginsubmit"]'),
            ('输入型登录按钮', 'xpath://input[@name="loginsubmit"]'),
            ('包含登录文本的按钮', 'xpath://button[contains(text(), "登录")]'),
            ('特定类名按钮', 'xpath://button[contains(@class, "pn")]'),
            ('值为true的按钮', 'xpath://button[@value="true"]')
        ]
        
        login_found = False
        for name, selector in login_selectors:
            element = page.ele(selector, timeout=2)
            if element:
                print(f"  ✅ {name}: 找到")
                print(f"     标签: {element.tag}")
                print(f"     Name: {element.attr('name')}")
                print(f"     Class: {element.attr('class')}")
                print(f"     Value: {element.attr('value')}")
                print(f"     文本: {element.text}")
                login_found = True
            else:
                print(f"  ❌ {name}: 未找到")
        
        print("-" * 30)
        
        # 检查验证码
        print("🔍 查找验证码:")
        
        # 数学验证码
        math_selectors = [
            ('通用验证码', 'xpath://div[contains(@class, "secqaa")]'),
            ('验证码问题', 'xpath://*[contains(text(), "=")]'),
            ('验证码输入框', 'xpath://input[contains(@name, "secanswer")]')
        ]
        
        for name, selector in math_selectors:
            element = page.ele(selector, timeout=2)
            if element:
                print(f"  ✅ {name}: 找到")
                if element.text:
                    print(f"     内容: {element.text}")
            else:
                print(f"  ❌ {name}: 未找到")
        
        # 滑块验证码
        slider_keywords = ['滑块', 'slider', 'captcha', '验证']
        slider_found = False
        for keyword in slider_keywords:
            if keyword in page.html.lower():
                print(f"  ✅ 可能存在滑块验证码 (关键词: {keyword})")
                slider_found = True
                break
        
        if not slider_found:
            print("  ❌ 未发现滑块验证码")
        
        print("-" * 50)
        
        # 总结
        print("📋 元素检查总结:")
        print(f"  用户名输入框: {'✅' if username_found else '❌'}")
        print(f"  密码输入框: {'✅' if password_found else '❌'}")
        print(f"  登录按钮: {'✅' if login_found else '❌'}")
        
        if username_found and password_found and login_found:
            print("\n🎉 所有必要的登录元素都找到了！")
        else:
            print("\n⚠️ 部分登录元素未找到，可能需要调整选择器")
        
        # 显示页面源码片段（用于调试）
        print("\n" + "=" * 50)
        print("📄 页面源码片段（前1000字符）:")
        print(page.html[:1000])
        
        print("\n" + "=" * 50)
        print("测试完成！请查看浏览器窗口确认页面状态。")
        print("按回车键关闭浏览器...")
        input()
        
        page.quit()
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        try:
            page.quit()
        except:
            pass
        return False

def main():
    """主函数"""
    print("🔍 086wow.cn页面元素测试工具")
    print("此工具用于详细检查登录页面的所有元素")
    print("=" * 50)
    
    # 检查依赖
    try:
        from DrissionPage import ChromiumPage, ChromiumOptions
        print("✅ DrissionPage 导入成功")
    except ImportError as e:
        print(f"❌ DrissionPage 导入失败: {e}")
        print("请先安装: pip install DrissionPage")
        return
    
    print("\n开始测试...")
    test_page_elements()

if __name__ == "__main__":
    main()
